/**
 * 超声图像查看器相关类型定义
 */

import type * as fabric from 'fabric'

// 图像信息接口
export interface ImageInfo {
  id: string
  name: string
  url: string
  width: number
  height: number
  size: number
  format: string
  createdAt: string
}

// 缩放配置接口
export interface ZoomConfig {
  min: number
  max: number
  step: number
  default: number
}

// 视图状态接口
export interface ViewState {
  zoom: number
  panX: number
  panY: number
  rotation: number
}

// 工具栏按钮类型
export type ToolbarAction = 
  | 'zoom-in'
  | 'zoom-out' 
  | 'zoom-fit'
  | 'zoom-actual'
  | 'reset'
  | 'rotate-left'
  | 'rotate-right'
  | 'fullscreen'

// 工具栏按钮配置
export interface ToolbarButton {
  action: ToolbarAction
  icon: string
  tooltip: string
  disabled?: boolean
}

// Fabric Canvas 实例类型
export type FabricCanvas = fabric.Canvas

// 图像加载状态
export type LoadingState = 'idle' | 'loading' | 'loaded' | 'error'

// 组件事件类型
export interface ImageViewerEvents {
  onImageLoad: (image: ImageInfo) => void
  onImageError: (error: Error) => void
  onZoomChange: (zoom: number) => void
  onViewChange: (state: ViewState) => void
}
