<script lang="ts" setup>
import { computed } from 'vue'
import type { ImageInfo } from '../types'

interface Props {
  imageInfo: ImageInfo | null
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  } catch {
    return dateString
  }
}

// 计算显示的图像信息
const displayInfo = computed(() => {
  if (!props.imageInfo) return null
  
  return {
    name: props.imageInfo.name,
    dimensions: `${props.imageInfo.width} × ${props.imageInfo.height}`,
    size: formatFileSize(props.imageInfo.size),
    format: props.imageInfo.format.toUpperCase(),
    createdAt: formatDate(props.imageInfo.createdAt)
  }
})
</script>

<template>
  <div class="image-info">
    <!-- 加载状态 -->
    <div v-if="loading" class="info-loading">
      <el-skeleton :rows="3" animated />
    </div>

    <!-- 图像信息 -->
    <div v-else-if="displayInfo" class="info-content">
      <div class="info-item">
        <span class="info-label">文件名:</span>
        <span class="info-value" :title="displayInfo.name">{{ displayInfo.name }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label">尺寸:</span>
        <span class="info-value">{{ displayInfo.dimensions }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label">大小:</span>
        <span class="info-value">{{ displayInfo.size }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label">格式:</span>
        <span class="info-value">{{ displayInfo.format }}</span>
      </div>
      
      <div class="info-item">
        <span class="info-label">创建时间:</span>
        <span class="info-value">{{ displayInfo.createdAt }}</span>
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="info-empty">
      <el-empty description="暂无图像信息" :image-size="60" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-info {
  padding: 16px;
  background: var(--el-bg-color);
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);

  .info-loading {
    padding: 8px 0;
  }

  .info-content {
    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        min-width: 80px;
        font-size: 13px;
        color: var(--el-text-color-regular);
        font-weight: 500;
      }

      .info-value {
        flex: 1;
        font-size: 13px;
        color: var(--el-text-color-primary);
        word-break: break-all;
        line-height: 1.4;
      }
    }
  }

  .info-empty {
    text-align: center;
    padding: 20px 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-info {
    padding: 12px;

    .info-content .info-item {
      flex-direction: column;
      align-items: flex-start;
      margin-bottom: 8px;

      .info-label {
        min-width: auto;
        margin-bottom: 4px;
        font-size: 12px;
      }

      .info-value {
        font-size: 12px;
      }
    }
  }
}
</style>
