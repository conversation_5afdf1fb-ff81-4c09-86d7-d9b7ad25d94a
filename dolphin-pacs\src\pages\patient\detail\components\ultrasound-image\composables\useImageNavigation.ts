import { ref, onUnmounted, readonly, type Ref } from 'vue'
import * as fabric from 'fabric'

interface Point {
  x: number
  y: number
}

interface NavigationOptions {
  maxZoom?: number
  minZoom?: number
  zoomSpeed?: number
}

export function useImageNavigation(
  canvas: Ref<fabric.Canvas | null>,
  onZoomChange?: (zoom: number) => void,
  options: NavigationOptions = {}
) {
  const {
    maxZoom = 10,
    minZoom = 0.1,
    zoomSpeed = 0.999
  } = options

  const isDragging = ref(false)
  const lastPanPoint = ref<Point>({ x: 0, y: 0 })
  let retryCount = 0
  const maxRetries = 10
  let retryTimeout: number | null = null
  let isInitialized = false

  const isCanvasReady = (): boolean => {
    if (!canvas.value) {
      console.log('Canvas is null')
      return false
    }
    try {
      const hasRequiredMethods = !!(
        typeof canvas.value.on === 'function' &&
        typeof canvas.value.off === 'function' &&
        typeof canvas.value.getElement === 'function'
      )

      if (!hasRequiredMethods) {
        console.log('Canvas missing required methods')
        return false
      }

      const element = canvas.value.getElement()
      if (!element || !element.parentNode) {
        console.log('Canvas element not in DOM')
        return false
      }

      if (!canvas.value.viewportTransform) {
        console.log('Canvas viewportTransform not ready')
        return false
      }

      return true
    } catch (error) {
      console.warn('Canvas readiness check failed:', error)
      return false
    }
  }

  // 存储事件处理器引用，以便正确移除
  let mouseDownHandler: ((opt: any) => void) | null = null
  let mouseMoveHandler: ((opt: any) => void) | null = null
  let mouseUpHandler: (() => void) | null = null
  let mouseWheelHandler: ((opt: any) => void) | null = null

  const enablePanning = () => {
    console.log('enablePanning called, retryCount:', retryCount)

    // 如果已经初始化过，先清理
    if (isInitialized) {
      disablePanning()
    }

    if (!isCanvasReady()) {
      console.log('Canvas not ready, retrying...')
      if (retryCount < maxRetries) {
        retryCount++
        retryTimeout = window.setTimeout(() => enablePanning(), 500)
        return
      }
      console.error('Failed to initialize canvas navigation after maximum retries')
      return
    }

    try {
      console.log('Attempting to bind events...')

      // 最后一次检查
      if (!canvas.value) {
        throw new Error('Canvas is null')
      }

      if (typeof canvas.value.on !== 'function') {
        throw new Error('Canvas.on is not a function')
      }

      mouseDownHandler = (opt: any) => {
        const evt = opt.e as MouseEvent
        if (evt.altKey || evt.button === 1) {
          isDragging.value = true
          if (canvas.value) {
            canvas.value.selection = false
            lastPanPoint.value = { x: evt.clientX, y: evt.clientY }
            canvas.value.setCursor('grabbing')
          }
        }
      }

      mouseMoveHandler = (opt: any) => {
        if (!isDragging.value || !canvas.value) return
        const evt = opt.e as MouseEvent
        const vpt = canvas.value.viewportTransform
        if (!vpt) return
        vpt[4] += evt.clientX - lastPanPoint.value.x
        vpt[5] += evt.clientY - lastPanPoint.value.y
        canvas.value.requestRenderAll()
        lastPanPoint.value = { x: evt.clientX, y: evt.clientY }
      }

      mouseUpHandler = () => {
        if (!canvas.value) return
        const vpt = canvas.value.viewportTransform
        if (vpt) {
          canvas.value.setViewportTransform(vpt)
        }
        isDragging.value = false
        canvas.value.selection = true
        canvas.value.setCursor('default')
      }

      mouseWheelHandler = (opt: any) => {
        if (!canvas.value) return
        const evt = opt.e as WheelEvent
        let zoom = canvas.value.getZoom()
        zoom *= zoomSpeed ** evt.deltaY
        zoom = Math.max(minZoom, Math.min(maxZoom, zoom))

        const point = new fabric.Point(evt.offsetX, evt.offsetY)
        canvas.value.zoomToPoint(point, zoom)
        onZoomChange?.(zoom)
        evt.preventDefault()
        evt.stopPropagation()
      }

      // 绑定事件
      console.log('Binding events to canvas...')
      canvas.value.on('mouse:down', mouseDownHandler)
      canvas.value.on('mouse:move', mouseMoveHandler)
      canvas.value.on('mouse:up', mouseUpHandler)
      canvas.value.on('mouse:wheel', mouseWheelHandler)

      isInitialized = true
      retryCount = 0
      console.log('✅ Image navigation initialized successfully')

    } catch (error) {
      console.error('❌ Failed to initialize navigation:', error)
      isInitialized = false

      // 如果失败，尝试重试
      if (retryCount < maxRetries) {
        retryCount++
        console.log(`Retrying in 1 second... (attempt ${retryCount}/${maxRetries})`)
        retryTimeout = window.setTimeout(() => enablePanning(), 1000)
      } else {
        console.error('Maximum retries reached, giving up')
      }
    }
  }

  const disablePanning = () => {
    console.log('disablePanning called')

    if (retryTimeout) {
      clearTimeout(retryTimeout)
      retryTimeout = null
    }

    if (!canvas.value || !isInitialized) {
      console.log('Canvas not available or not initialized, skipping cleanup')
      return
    }

    try {
      // 使用存储的处理器引用移除事件监听器
      if (mouseDownHandler) {
        canvas.value.off('mouse:down', mouseDownHandler)
        mouseDownHandler = null
      }
      if (mouseMoveHandler) {
        canvas.value.off('mouse:move', mouseMoveHandler)
        mouseMoveHandler = null
      }
      if (mouseUpHandler) {
        canvas.value.off('mouse:up', mouseUpHandler)
        mouseUpHandler = null
      }
      if (mouseWheelHandler) {
        canvas.value.off('mouse:wheel', mouseWheelHandler)
        mouseWheelHandler = null
      }

      isInitialized = false
      console.log('✅ Navigation events cleaned up')
    } catch (error) {
      console.warn('Failed to remove navigation listeners:', error)
    }
  }

  const resetViewport = () => {
    if (!canvas.value) return
    canvas.value.setViewportTransform([1, 0, 0, 1, 0, 0])
    canvas.value.setZoom(1)
    canvas.value.requestRenderAll()
  }

  const centerView = () => {
    if (!canvas.value) return
    const objects = canvas.value.getObjects()
    if (!objects.length) return

    // 计算所有对象的边界框
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity

    objects.forEach(obj => {
      const bound = obj.getBoundingRect()
      minX = Math.min(minX, bound.left)
      minY = Math.min(minY, bound.top)
      maxX = Math.max(maxX, bound.left + bound.width)
      maxY = Math.max(maxY, bound.top + bound.height)
    })

    const width = maxX - minX
    const height = maxY - minY
    const { width: canvasWidth = 0, height: canvasHeight = 0 } = canvas.value
    canvas.value.setViewportTransform([1, 0, 0, 1, (canvasWidth - width) / 2, (canvasHeight - height) / 2])
    canvas.value.requestRenderAll()
  }

  const panTo = (x: number, y: number) => {
    if (!canvas.value) return
    const vpt = canvas.value.viewportTransform!
    vpt[4] = x
    vpt[5] = y
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  const getPanPosition = (): Point => {
    if (!canvas.value) return { x: 0, y: 0 }
    const vpt = canvas.value.viewportTransform!
    return { x: vpt[4], y: vpt[5] }
  }

  const handleKeyNavigation = (event: KeyboardEvent) => {
    if (!canvas.value) return
    const step = 10
    const vpt = canvas.value.viewportTransform!
    switch (event.key) {
      case 'ArrowUp': vpt[5] += step; break
      case 'ArrowDown': vpt[5] -= step; break
      case 'ArrowLeft': vpt[4] += step; break
      case 'ArrowRight': vpt[4] -= step; break
      default: return
    }
    event.preventDefault()
    canvas.value.setViewportTransform(vpt)
    canvas.value.requestRenderAll()
  }

  onUnmounted(() => {
    disablePanning()
    document.removeEventListener('keydown', handleKeyNavigation)
  })

  return {
    isDragging: readonly(isDragging),
    enablePanning,
    disablePanning,
    resetViewport,
    centerView,
    panTo,
    getPanPosition,
    handleKeyNavigation
  }
}