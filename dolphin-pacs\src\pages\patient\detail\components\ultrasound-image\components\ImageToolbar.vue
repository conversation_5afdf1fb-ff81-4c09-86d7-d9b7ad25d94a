<script lang="ts" setup>
import { computed } from 'vue'
import type { ToolbarButton, ToolbarAction } from '../types'

interface Props {
  zoomPercentage: number
  canZoomIn: boolean
  canZoomOut: boolean
  loading?: boolean
}

interface Emits {
  (e: 'action', action: ToolbarAction): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 工具栏按钮配置
const toolbarButtons = computed<ToolbarButton[]>(() => [
  {
    action: 'zoom-in',
    icon: 'ZoomIn',
    tooltip: '放大',
    disabled: !props.canZoomIn || props.loading
  },
  {
    action: 'zoom-out',
    icon: 'ZoomOut', 
    tooltip: '缩小',
    disabled: !props.canZoomOut || props.loading
  },
  {
    action: 'zoom-fit',
    icon: 'FullScreen',
    tooltip: '适应窗口',
    disabled: props.loading
  },
  {
    action: 'zoom-actual',
    icon: 'Crop',
    tooltip: '实际大小',
    disabled: props.loading
  },
  {
    action: 'rotate-left',
    icon: 'RefreshLeft',
    tooltip: '向左旋转',
    disabled: props.loading
  },
  {
    action: 'rotate-right',
    icon: 'RefreshRight',
    tooltip: '向右旋转',
    disabled: props.loading
  },
  {
    action: 'reset',
    icon: 'Refresh',
    tooltip: '重置视图',
    disabled: props.loading
  }
])

// 处理按钮点击
const handleAction = (action: ToolbarAction) => {
  emit('action', action)
}
</script>

<template>
  <div class="image-toolbar">
    <!-- 缩放信息 -->
    <div class="zoom-info">
      <span class="zoom-text">{{ zoomPercentage }}%</span>
    </div>

    <!-- 工具按钮 -->
    <div class="toolbar-buttons">
      <el-button-group>
        <el-tooltip
          v-for="button in toolbarButtons"
          :key="button.action"
          :content="button.tooltip"
          placement="top"
        >
          <el-button
            :icon="button.icon"
            :disabled="button.disabled"
            size="small"
            @click="handleAction(button.action)"
          />
        </el-tooltip>
      </el-button-group>
    </div>

    <!-- 全屏按钮 -->
    <div class="fullscreen-button">
      <el-tooltip content="全屏查看" placement="top">
        <el-button
          icon="FullScreen"
          :disabled="loading"
          size="small"
          type="primary"
          @click="handleAction('fullscreen')"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  gap: 16px;

  .zoom-info {
    min-width: 60px;
    
    .zoom-text {
      font-size: 14px;
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }

  .toolbar-buttons {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  .fullscreen-button {
    min-width: 60px;
    display: flex;
    justify-content: flex-end;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .image-toolbar {
    padding: 6px 12px;
    gap: 8px;

    .zoom-info .zoom-text {
      font-size: 12px;
    }

    :deep(.el-button) {
      padding: 4px 6px;
    }
  }
}
</style>
