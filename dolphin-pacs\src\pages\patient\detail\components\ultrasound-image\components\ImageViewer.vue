<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Picture } from '@element-plus/icons-vue'
import { useFabricCanvas } from '../composables/useFabricCanvas'
import { useImageZoom } from '../composables/useImageZoom'
import { useImageNavigation } from '../composables/useImageNavigation'
import type { ImageInfo, LoadingState } from '../types'

interface Props {
  imageInfo: ImageInfo | null
}

interface Emits {
  (e: 'load', imageInfo: ImageInfo): void
  (e: 'error', error: Error): void
  (e: 'zoom-change', zoom: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const canvasContainer = ref<HTMLDivElement>()
const loadingState = ref<LoadingState>('idle')
const canvasId = 'ultrasound-canvas'

const { canvas, isReady, initCanvas, loadImage, fitToCanvas, setZoom, getZoom, resetView, rotateImage, resizeCanvas } =
  useFabricCanvas(canvasId)
const { currentZoom, zoomPercentage, canZoomIn, canZoomOut, zoomIn, zoomOut, resetZoom, actualSize } = useImageZoom({
  min: 0.1,
  max: 10,
  step: 0.25,
  default: 1
})

const handleZoomChange = (zoom: number) => {
  currentZoom.value = zoom
  emit('zoom-change', zoom)
}

const { enablePanning, handleKeyNavigation } = useImageNavigation(canvas, handleZoomChange, {
  maxZoom: 10,
  minZoom: 0.1,
  zoomSpeed: 0.999
})

watch(currentZoom, (newZoom) => {
  setZoom(newZoom)
})

watch(
  () => props.imageInfo,
  async (newImageInfo) => {
    if (newImageInfo && isReady.value) {
      await loadImageData(newImageInfo)
    }
  },
  { immediate: true }
)

async function loadImageData(imageInfo: ImageInfo) {
  try {
    loadingState.value = 'loading'
    await loadImage(imageInfo)
    loadingState.value = 'loaded'
    resetZoom()
    emit('load', imageInfo)
  } catch (error) {
    loadingState.value = 'error'
    const errorObj = error instanceof Error ? error : new Error('Failed to load image')
    ElMessage.error(errorObj.message)
    emit('error', errorObj)
  }
}

const handleResize = () => {
  if (canvasContainer.value && canvas.value) {
    const { clientWidth, clientHeight } = canvasContainer.value
    resizeCanvas(clientWidth, clientHeight)
    fitToCanvas()
  }
}

onMounted(async () => {
  try {
    await nextTick()
    await initCanvas()

    if (!isReady.value || !canvas.value) {
      throw new Error('Canvas initialization failed')
    }

    if (canvasContainer.value) {
      handleResize()
    }

    // 等待更长时间确保 canvas 完全初始化
    await new Promise(resolve => setTimeout(resolve, 200))

    // 再次检查 canvas 状态
    if (isReady.value && canvas.value && typeof canvas.value.on === 'function') {
      enablePanning()
      document.addEventListener('keydown', handleKeyNavigation)
    } else {
      console.warn('Canvas not ready for navigation, will retry later')
      // 延迟重试
      setTimeout(() => {
        if (isReady.value && canvas.value && typeof canvas.value.on === 'function') {
          enablePanning()
          document.addEventListener('keydown', handleKeyNavigation)
        }
      }, 500)
    }

    if (props.imageInfo) {
      await loadImageData(props.imageInfo)
    }

    window.addEventListener('resize', handleResize)
  } catch (error) {
    console.error('Failed to initialize ImageViewer:', error)
    ElMessage.error('Failed to initialize image viewer')
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  document.removeEventListener('keydown', handleKeyNavigation)
})

const rotateLeft = () => rotateImage(-90)
const rotateRight = () => rotateImage(90)

defineExpose({
  fitToCanvas,
  resetView,
  zoomIn,
  zoomOut,
  actualSize,
  getZoom,
  rotateLeft,
  rotateRight
})
</script>

<template>
  <div class="image-viewer">
    <div ref="canvasContainer" class="canvas-container">
      <canvas :id="canvasId" class="fabric-canvas" />
      
      <div v-if="loadingState === 'loading'" class="overlay loading-overlay">
        <el-icon class="icon is-loading"><Loading /></el-icon>
        <span class="overlay-text">Loading image...</span>
      </div>
      
      <div v-else-if="loadingState === 'error'" class="overlay error-overlay">
        <el-icon class="icon"><Warning /></el-icon>
        <span class="overlay-text">Failed to load image</span>
      </div>
      
      <div v-else-if="!imageInfo" class="overlay empty-overlay">
        <el-icon class="icon"><Picture /></el-icon>
        <span class="overlay-text">Please select an ultrasound image</span>
      </div>
    </div>

    <div class="operation-tips">
      <span class="tip-text">💡 Tip: Hold Alt and drag to pan, use mouse wheel to zoom</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 0;

  .canvas-container {
    position: relative;
    flex: 1;
    width: 100%;
    border: 1px solid var(--el-border-color-light);
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;

    .fabric-canvas {
      width: 100%;
      height: 100%;
    }

    .overlay {
      position: absolute;
      inset: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(2px);

      .icon {
        font-size: 48px;
        margin-bottom: 12px;
      }

      .overlay-text {
        font-size: 14px;
        color: var(--el-text-color-regular);
      }

      &.error-overlay .icon {
        color: var(--el-color-danger);
      }

      &.empty-overlay .icon {
        color: var(--el-text-color-placeholder);
      }
    }
  }

  .operation-tips {
    padding: 8px 12px;
    background: var(--el-bg-color-page);
    border-radius: 4px;
    margin-top: 8px;

    .tip-text {
      font-size: 12px;
      color: var(--el-text-color-regular);
    }
  }
}

@media (max-width: 768px) {
  .image-viewer {
    .operation-tips {
      padding: 6px 8px;
      
      .tip-text {
        font-size: 11px;
      }
    }
  }
}
</style>